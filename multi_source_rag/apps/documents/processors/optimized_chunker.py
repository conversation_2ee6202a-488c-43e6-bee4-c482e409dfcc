"""
Optimized Chunker using LlamaIndex node parsers.

This module provides a unified chunking interface that uses LlamaIndex's
advanced node parsers for optimal chunking strategies based on content type.
"""

import logging
from typing import Any, Dict, List, Optional

from llama_index.core.node_parser import (
    <PERSON>tenceSplitter,
    CodeSplitter,
    SemanticSplitterNodeParser
)
from llama_index.core import Document

logger = logging.getLogger(__name__)


class OptimizedChunker:
    """
    Optimized chunker that uses LlamaIndex node parsers for intelligent chunking.
    
    This chunker automatically selects the best chunking strategy based on content type
    and provides consistent chunking across the application.
    """
    
    def __init__(
        self,
        default_chunk_size: int = 1000,
        default_chunk_overlap: int = 100,
        default_separators: Optional[List[str]] = None
    ):
        """
        Initialize the optimized chunker.
        
        Args:
            default_chunk_size: Default chunk size for text splitting
            default_chunk_overlap: Default overlap between chunks
            default_separators: Default separators for text splitting
        """
        self.default_chunk_size = default_chunk_size
        self.default_chunk_overlap = default_chunk_overlap
        self.default_separators = default_separators or ["\n\n", "\n", " ", ""]
        
        # Initialize node parsers
        self._init_node_parsers()
    
    def _init_node_parsers(self):
        """Initialize LlamaIndex node parsers for different content types."""
        # Text/conversation parser
        self.text_parser = SentenceSplitter(
            chunk_size=self.default_chunk_size,
            chunk_overlap=self.default_chunk_overlap,
            separator=" "
        )
        
        # Code parser
        self.code_parser = CodeSplitter(
            language="python",
            chunk_lines=40,
            chunk_overlap=15
        )
        
        # Semantic parser for documents
        self.semantic_parser = SemanticSplitterNodeParser(
            buffer_size=1,
            breakpoint_percentile_threshold=95
        )
    
    def chunk_document(
        self,
        content: str,
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Chunk a document using the appropriate strategy.
        
        Args:
            content: Document content to chunk
            content_type: Type of content (text, code, slack, etc.)
            metadata: Additional metadata for the document
            
        Returns:
            List of chunk dictionaries
        """
        if not content or not content.strip():
            return []
        
        metadata = metadata or {}
        
        # Create LlamaIndex document
        doc = Document(text=content, metadata=metadata)
        
        # Select appropriate parser based on content type
        if content_type in ["code", "python", "javascript", "java", "cpp"]:
            parser = self.code_parser
        elif content_type in ["text/slack", "conversation", "chat"]:
            parser = self.text_parser
        elif content_type in ["document", "markdown", "text"]:
            parser = self.semantic_parser
        else:
            # Default to text parser
            parser = self.text_parser
        
        try:
            # Parse document into nodes
            nodes = parser.get_nodes_from_documents([doc])
            
            # Convert nodes to chunk dictionaries
            chunks = []
            for i, node in enumerate(nodes):
                chunk = {
                    "text": node.text,
                    "metadata": {
                        **metadata,
                        "chunk_index": i,
                        "total_chunks": len(nodes),
                        "node_id": node.node_id,
                        "content_type": content_type
                    }
                }
                chunks.append(chunk)
            
            logger.debug(f"Created {len(chunks)} chunks from {content_type} content")
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking document: {str(e)}")
            # Fallback to simple text splitting
            return self._fallback_chunk(content, metadata)
    
    def _fallback_chunk(
        self,
        content: str,
        metadata: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Fallback chunking method using simple text splitting.
        
        Args:
            content: Content to chunk
            metadata: Metadata for chunks
            
        Returns:
            List of chunk dictionaries
        """
        chunks = []
        content_length = len(content)
        
        if content_length <= self.default_chunk_size:
            return [{
                "text": content,
                "metadata": {
                    **metadata,
                    "chunk_index": 0,
                    "total_chunks": 1
                }
            }]
        
        # Split into chunks with overlap
        start = 0
        chunk_index = 0
        
        while start < content_length:
            end = min(start + self.default_chunk_size, content_length)
            chunk_text = content[start:end]
            
            chunks.append({
                "text": chunk_text,
                "metadata": {
                    **metadata,
                    "chunk_index": chunk_index,
                    "total_chunks": 0  # Will be updated later
                }
            })
            
            chunk_index += 1
            start = end - self.default_chunk_overlap
            
            if start >= content_length:
                break
        
        # Update total chunks count
        for chunk in chunks:
            chunk["metadata"]["total_chunks"] = len(chunks)
        
        return chunks
